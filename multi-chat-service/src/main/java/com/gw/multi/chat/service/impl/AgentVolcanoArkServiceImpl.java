package com.gw.multi.chat.service.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.gw.common.agent.vo.VolcanoArkSettingVO;
import com.gw.common.util.StringUtils;
import com.gw.multi.chat.config.VolcanoArkConfig;
import com.gw.multi.chat.constant.MultiChatConstant;
import com.gw.multi.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.multi.chat.dto.MessageContentDTO;
import com.gw.multi.chat.service.VolcanoArkService;
import com.gw.multi.chat.vo.AIResponseVO;
import com.gw.multi.chat.vo.ChatContextVO;
import com.volcengine.ark.runtime.Const;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.CreateContextResult;
import com.volcengine.ark.runtime.model.context.TruncationStrategy;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 基于火山方舟大模型服务平台的AgentRemoteService实现
 * 支持上下文缓存功能
 */
@Service("VolcanoArkService")
@Log4j2
@RequiredArgsConstructor
public class AgentVolcanoArkServiceImpl implements VolcanoArkService {

    private final VolcanoArkConfig volcanoArkConfig;


    /**
     * 获取火山方舟服务实例
     */
    private ArkService getArkService() {
        VolcanoArkSettingVO setting = getVolcanoArkSetting();
        return ArkService.builder()
                .apiKey(setting.getApiKey())
                .baseUrl(setting.getBaseUrl())
                .timeout(Duration.ofMillis(setting.getReadTimeout()))
                .connectTimeout(Duration.ofMillis(setting.getConnectTimeout()))
                .retryTimes(setting.getRetryTimes())
                .build();
    }

    /**
     * 获取火山方舟配置
     */
    private VolcanoArkSettingVO getVolcanoArkSetting() {
        VolcanoArkSettingVO setting = new VolcanoArkSettingVO();
        setting.setApiKey(volcanoArkConfig.getApiKey());
        setting.setBaseUrl(volcanoArkConfig.getBaseUrl());
        setting.setModelId(volcanoArkConfig.getDefaultModelId());
        setting.setConnectTimeout(volcanoArkConfig.getConnectTimeout());
        setting.setReadTimeout(volcanoArkConfig.getReadTimeout());
        setting.setRetryTimes(volcanoArkConfig.getRetryTimes());
        return setting;
    }

    /**
     * 创建上下文缓存
     * 如果上下文缓存功能不可用，会抛出异常
     */
    private CreateContextResult createContextCache(String modelId, List<ChatMessage> systemMessages,Integer ttl) {
        ArkService arkService = getArkService();
        TruncationStrategy truncationStrategy = TruncationStrategy.builder()
                .type(Const.TRUNCATION_STRATEGY_TYPE_ROLLING_TOKENS)
                .maxWindowTokens(32768)
                .rollingWindowTokens(4096)
                .rollingTokens(true)
                .build();
        // 构建请求 - 使用SDK提供的CreateContextRequest
        CreateContextRequest request = CreateContextRequest.builder()
                .model(modelId)
                // 设置上下文模式为会话模式
                .mode(Const.CONTEXT_MODE_SESSION)
                // 设置系统消息
                .messages(systemMessages)
                // 设置上下文的生存时间（秒）
                .truncationStrategy(truncationStrategy)
                .ttl(ttl)
                .build();

        log.info("创建上下文缓存请求: {}", JSON.toJSONString(request));

        // 使用SDK接口创建上下文缓存
        CreateContextResult result = arkService.createContext(request);

        log.info("创建上下文缓存成功: {}", JSON.toJSONString(result));

        return result;
    }

    @Override
    public String createConversation(List<MessageContentDTO> messages,Integer ttl) {
        String conversationId = UUID.randomUUID().toString();
        log.info("创建火山方舟会话: {}", conversationId);


        try {
            VolcanoArkSettingVO setting = getVolcanoArkSetting();

            // 创建系统消息（可以根据需要自定义）- 使用SDK的ChatMessage
            List<ChatMessage> systemMessages = new ArrayList<>();
            messages.forEach(msg -> systemMessages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(msg.getContent())
                    .build()));
            // 尝试创建上下文缓存
            CreateContextResult contextResult = createContextCache(setting.getModelId(), systemMessages,ttl);


            log.info("创建火山方舟会话成功 - 会话ID: {}, 缓存ID: {}", conversationId, contextResult.getId());
            return contextResult.getId();

        } catch (Exception e) {
            log.warn("创建火山方舟上下文缓存失败，可能当前模型不支持该功能或API不可用: {}", e.getMessage());

            // 如果是404错误，标记上下文缓存功能不可用
            if (e.getMessage() != null && e.getMessage().contains("404")) {
                log.warn("检测到404错误，标记上下文缓存功能为不可用状态");
            }

            // 返回普通的UUID作为会话ID，使用传统模式
            log.info("使用传统模式，返回会话ID: {}", conversationId);
            return conversationId;
        }
    }

    /**
     * 使用上下文缓存进行对话
     */
    private String chatWithContextCache(String contextCacheId, List<ChatMessage> messages) throws Exception {
        if (contextCacheId == null || contextCacheId.isEmpty()) {
            return "ResourceNotFound";
        }
        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        // 构建请求 - 使用SDK提供的ContextChatCompletionRequest
        ContextChatCompletionRequest request = ContextChatCompletionRequest.builder()
                .model(setting.getModelId())
                .contextId(contextCacheId)
                .messages(messages)
                .maxTokens(2048)
                .temperature(0.7)
                .stream(false)
                .build();

        log.info("对话请求: {}", JSON.toJSONString(request));

        // 使用SDK接口进行上下文缓存对话
        try {
            var response = arkService.createContextChatCompletion(request);

            log.info("对话响应: {}", JSON.toJSONString(response));

            // 从响应中提取内容
            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                var choice = response.getChoices().get(0);
                // 检查是否因为内容过长而结束
                if(choice.getFinishReason().equals("length")){
                    log.warn("内容过长，触发重建上下文机制: contextId={}, response={}", contextCacheId, response);
                    return "ContentTooLong";
                }
                var message = choice.getMessage();
                if (message != null && message.getContent() != null) {
                    return String.valueOf(message.getContent());
                }
            }
        } catch (ArkHttpException e) {
            log.error("上下文缓存对话处理异常", e);
            if (e.code.equals("ResourceNotFound")) {
                // 如果是404错误，标记上下文缓存功能不可用
                return "ResourceNotFound";
            }
            throw e;
        }
        throw new Exception("无法从响应中解析出内容");
    }


    @Override
    public AIResponseVO sendChatMessage(String conversationId, List<ChatContextVO> message, List<MessageContentDTO> systemMessages,
                                        ResourceNotFoundCallback callback) throws Exception {

        log.info("火山方舟发送聊天消息（带回调） - 会话ID: {}", conversationId);
        log.info("消息列表: {}", JSON.toJSONString(message));
        return sendChatMessageWithContextCacheAndCallback(conversationId, systemMessages, message, callback);
    }

    /**
     * 使用上下文缓存进行对话（带回调）
     */
    private AIResponseVO sendChatMessageWithContextCacheAndCallback(String contextCacheId, List<MessageContentDTO> systemMessages,
                                                                   List<ChatContextVO> message,
                                                                   ResourceNotFoundCallback callback) throws Exception {

        log.info("开始上下文缓存对话（带回调）");


        // 构建聊天消息列表
        List<ChatMessage> messages = new ArrayList<>();
        for (ChatContextVO context : message) {
            if (StringUtils.hasText(context.getContent())) {
                ChatMessageRole role = mapToArkRole(context.getRole());
                if (role != null) {
                    messages.add(ChatMessage.builder()
                            .role(role)
                            .content(context.getContent())
                            .build());
                }
            }
        }

        if (messages.isEmpty()) {
            throw new Exception("没有有效的消息内容");
        }

        try {
            // 调用上下文缓存对话API
            String responseContent = chatWithContextCache(contextCacheId, messages);

            // 如果上下文缓存资源未找到，触发回调并尝试重新创建
            if ("ResourceNotFound".equals(responseContent)) {
                log.info("上下文缓存资源未找到，触发回调: contextCacheId={}", contextCacheId);

                // 构建系统上下文信息
                SystemContext systemContext = new SystemContext(
                    contextCacheId,
                    systemMessages,
                    message,
                    true // 假设上下文缓存可用
                );

                CreateRemoteSessionParamsDTO sessionParams = null;
                // 触发回调
                if (callback != null) {
                    try {
                        sessionParams = callback.onResourceNotFound(contextCacheId, systemContext, "上下文缓存资源未找到");
                        if (sessionParams != null && sessionParams.getContexts() != null) {
                            for (MessageContentDTO item : sessionParams.getContexts()) {
                                log.info("回调返回的系统消息: {}", item.getContent());
                            }
                        }
                    } catch (Exception callbackException) {
                        log.error("回调执行异常: contextCacheId={}", contextCacheId, callbackException);
                    }
                }

                if (sessionParams == null || sessionParams.getContexts() == null) {
                    log.error("回调未返回有效的系统消息，回退到传统模式");
                    throw new Exception("上下文缓存功能不可用");
                }

                // 尝试重新创建上下文缓存
                String newContextCacheId = createConversation(sessionParams.getContexts(),sessionParams.getTtl());
                responseContent = chatWithContextCache(newContextCacheId, messages);

                if ("ResourceNotFound".equals(responseContent)) {
                    log.error("重新创建上下文缓存后仍然失败，回退到传统模式");
                    throw new Exception("上下文缓存功能不可用");
                }
                contextCacheId = newContextCacheId;
            }

            // 如果内容过长，触发回调并尝试重新创建（只执行一次）
            if ("ContentTooLong".equals(responseContent)) {
                log.info("内容过长，触发回调重建上下文: contextCacheId={}", contextCacheId);

                // 构建系统上下文信息
                SystemContext systemContext = new SystemContext(
                    contextCacheId,
                    systemMessages,
                    message,
                    true // 假设上下文缓存可用
                );

                CreateRemoteSessionParamsDTO sessionParams = null;
                // 触发回调
                if (callback != null) {
                    try {
                        sessionParams = callback.onContentTooLong(contextCacheId, systemContext, "内容过长，需要重建上下文");
                        if (sessionParams != null && sessionParams.getContexts() != null) {
                            for (MessageContentDTO item : sessionParams.getContexts()) {
                                log.info("内容过长回调返回的系统消息: {}", item.getContent());
                            }
                        }
                    } catch (Exception callbackException) {
                        log.error("内容过长回调执行异常: contextCacheId={}", contextCacheId, callbackException);
                    }
                }

                if (sessionParams == null || sessionParams.getContexts() == null) {
                    log.error("内容过长回调未返回有效的系统消息，回退到传统模式");
                    throw new Exception("内容过长，上下文缓存功能不可用");
                }

                // 尝试重新创建上下文缓存
                String newContextCacheId = createConversation(sessionParams.getContexts(),sessionParams.getTtl());
                responseContent = chatWithContextCache(newContextCacheId, messages);

                // 如果重试后仍然是内容过长，回退到传统模式（只重试一次）
                if ("ContentTooLong".equals(responseContent)) {
                    log.error("重新创建上下文缓存后仍然内容过长，回退到传统模式");
                    throw new Exception("内容过长，上下文缓存功能不可用");
                }
                contextCacheId = newContextCacheId;
            }

            // 构建AI响应
            AIResponseVO aiResponse = new AIResponseVO();
            aiResponse.setRemoteContextId(contextCacheId);
            aiResponse.setStatus("completed");
            aiResponse.setCreatedAt(LocalDateTime.now());
            aiResponse.setCompletedAt(LocalDateTime.now());

            List<ChatContextVO> contexts = new ArrayList<>();

            ChatContextVO context = ChatContextVO.builder()
                    .role(MultiChatConstant.CHAT_ASSISTANT_ROLE)
                    .type(MultiChatConstant.CHAT_MSG_ANSWER_TYPE)
                    .content(responseContent)
                    .contentType(MultiChatConstant.CHAT_CONTENT_TYPE_TEXT)
                    .build();

            contexts.add(context);

            aiResponse.setContexts(contexts);
            aiResponse.setFollowMsg(new JSONArray());

            log.info("上下文缓存对话完成（带回调），生成了 {} 个上下文", contexts.size());
            return aiResponse;

        } catch (Exception e) {
            log.error("上下文缓存对话失败，回退到传统模式", e);
            // 如果上下文缓存对话失败，回退到传统模式
            return sendChatMessageTraditional(message);
        }
    }

    /**
     * 传统方式进行对话（不使用上下文缓存）
     */
    @Override
    public AIResponseVO sendChatMessageTraditional(List<ChatContextVO> message) throws Exception {

        log.info("使用传统模式进行对话");

        ArkService arkService = getArkService();

        // 构建聊天消息列表
        List<ChatMessage> chatMessages = new ArrayList<>();

        for (ChatContextVO item : message) {
            if (item.getRole() == null || item.getContent() == null) {
                continue;
            }

            ChatMessageRole role = mapToArkRole(item.getRole());
            if (role != null) {
                ChatMessage chatMessage = ChatMessage.builder()
                        .role(role)
                        .content(item.getContent())
                        .build();
                chatMessages.add(chatMessage);
            }
        }

        // 构建请求
        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model("ep-20250706151033-n28bn")
                .messages(chatMessages)
                .maxTokens(16288)
                .temperature(0.7)
                .build();

        log.info("发送请求到火山方舟");

        try {
            // 调用火山方舟API
            var response = arkService.createChatCompletion(request);

            AIResponseVO aiResponse = new AIResponseVO();
            aiResponse.setStatus("completed");
            aiResponse.setCreatedAt(LocalDateTime.now());
            aiResponse.setCompletedAt(LocalDateTime.now());

            List<ChatContextVO> contexts = new ArrayList<>();

            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                var choice = response.getChoices().get(0);
                var responseMessage = choice.getMessage();

                if (responseMessage != null && responseMessage.getContent() != null) {
                    String responseContent = String.valueOf(responseMessage.getContent());

                    ChatContextVO context = ChatContextVO.builder()
                            .role(MultiChatConstant.CHAT_ASSISTANT_ROLE)
                            .type(MultiChatConstant.CHAT_MSG_ANSWER_TYPE)
                            .content(responseContent)
                            .contentType(MultiChatConstant.CHAT_CONTENT_TYPE_TEXT)
                            .build();

                    contexts.add(context);
                }
            }

            aiResponse.setContexts(contexts);
            aiResponse.setFollowMsg(new JSONArray());

            log.info("传统模式对话完成，生成了 {} 个上下文", contexts.size());
            return aiResponse;

        } catch (Exception e) {
            log.error("调用火山方舟API时出错", e);

            // 返回错误响应
            AIResponseVO errorResponse = new AIResponseVO();
            errorResponse.setStatus("failed");
            errorResponse.setCreatedAt(LocalDateTime.now());
            errorResponse.setFailedAt(LocalDateTime.now());
            errorResponse.setContexts(new ArrayList<>());
            errorResponse.setFollowMsg(new JSONArray());

            throw new Exception("火山方舟API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 映射角色到火山方舟SDK的角色类型
     */
    private ChatMessageRole mapToArkRole(String role) {
        return switch (role) {
            case MultiChatConstant.CHAT_USER_ROLE -> ChatMessageRole.USER;
            case MultiChatConstant.CHAT_ASSISTANT_ROLE -> ChatMessageRole.ASSISTANT;
            case MultiChatConstant.CHAT_SYSTEM_ROLE -> ChatMessageRole.SYSTEM;
            default -> {
                log.warn("未知的消息角色: {}", role);
                yield null;
            }
        };
    }
}