package com.gw.multi.chat.websocket.manager;

import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.multi.chat.config.CacheProperties;
import com.gw.multi.chat.config.ChatHistoryConfig;
import com.gw.multi.chat.constant.MemberCacheConstant;
import com.gw.multi.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.multi.chat.dto.MessageContentDTO;
import com.gw.multi.chat.dto.MultiChatMessageDTO;
import com.gw.multi.chat.entity.MultiChatMsg;
import com.gw.multi.chat.entity.MultiChatSession;
import com.gw.multi.chat.exception.MultiChatException;
import com.gw.multi.chat.service.MultiChatMsgService;
import com.gw.multi.chat.service.VolcanoArkService;
import com.gw.multi.chat.util.ChatUtils;
import com.gw.multi.chat.util.MultiAgentDialogueParser;
import com.gw.multi.chat.util.StringSimilarityUtil;
import com.gw.multi.chat.vo.AIResponseVO;
import com.gw.multi.chat.vo.ChatContextVO;
import com.gw.multi.chat.vo.ChatResponseVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.gw.multi.chat.constant.MultiChatConstant.*;

/**
 * 智能体响应管理器
 * 专门负责智能体决策、并发响应处理和结果汇总
 */
@Component("multiChatAgentResponseManager")  // 👈 避免Bean名称冲突
@Log4j2
public class AgentResponseManager {

    private final VolcanoArkService volcanoArkService;
    private final MultiChatMsgService multiChatMsgService;
    private final ChatHistoryConfig chatHistoryConfig;
    private final MembershipProxyService membershipProxyService;
    private final CacheProperties cacheProperties;


    /**
     * 🔄 WebSocket最大并发智能体响应数量
     * 控制同时处理的智能体响应请求数量，防止系统过载
     * 默认值：3
     */
    @Value("${multi-chat.websocket.max-concurrent-agent-responses:3}")
    private int maxConcurrentAgentResponses;

    /**
     * 🎯 多轮对话最大轮数配置
     * 设置单次用户消息可以触发的最大对话轮数
     * 范围：1-10轮，建议3-5轮以平衡体验和性能
     * 默认值：1轮（保守配置）
     */
    @Value("${multi-chat.agent.max-conversation-rounds:2}")
    private int maxConversationRounds;

    /**
     * 🤖 启用智能自动多轮对话
     * true：基于AI评分自动判断是否继续下一轮对话
     * false：仅在有@提及时才继续下一轮
     * 默认值：true（启用智能判断）
     */
    @Value("${multi-chat.agent.enable-auto-rounds:true}")
    private boolean enableAutoRounds;

    /**
     * 📊 自动触发下一轮对话的互动评分阈值
     * 范围：0.0-1.0，数值越高越难触发自动多轮
     * 0.3：低阈值，容易触发多轮互动
     * 0.5：中等阈值，平衡性能和体验（推荐）
     * 0.7：高阈值，只有高质量互动才触发
     * 默认值：0.7（较高阈值，保守配置）
     */
    @Value("${multi-chat.agent.round-trigger-threshold:0.7}")
    private double roundTriggerThreshold;

    /**
     * 🎭 每轮对话最大@提及数量限制
     * 防止智能体无限@其他角色导致的循环对话
     * 建议值：2-5个，过多会影响对话质量
     * 默认值：2个（保守配置）
     */
    @Value("${multi-chat.agent.max-mentions-per-round:2}")
    private int maxMentionsPerRound;

    /**
     * 🎯 角色名称相似度匹配最小阈值
     * 当精确匹配失败时，使用相似度匹配的最小阈值
     * 范围：0.0-1.0，数值越高匹配越严格
     * 0.3：宽松匹配，容易匹配到相似名称
     * 0.5：中等匹配，平衡准确性和容错性（推荐）
     * 0.7：严格匹配，只匹配高度相似的名称
     * 默认值：0.5（中等阈值）
     */
    @Value("${multi-chat.agent.name-similarity-threshold:0.5}")
    private double nameSimilarityThreshold;

    /**
     * 🚀 智能体响应异步处理线程池
     */
    private ExecutorService agentResponseExecutor;

    public AgentResponseManager(VolcanoArkService volcanoArkService,
                                MultiChatMsgService multiChatMsgService,
                                ChatHistoryConfig chatHistoryConfig,
                                MembershipProxyService membershipProxyService,
                                CacheProperties cacheProperties) {
        this.volcanoArkService = volcanoArkService;
        this.multiChatMsgService = multiChatMsgService;
        this.chatHistoryConfig = chatHistoryConfig;
        this.membershipProxyService = membershipProxyService;
        this.cacheProperties = cacheProperties;
    }

    /**
     * 初始化线程池
     */
    @PostConstruct
    public void initExecutor() {
        this.agentResponseExecutor = Executors.newFixedThreadPool(
                Runtime.getRuntime().availableProcessors() * 2,
                r -> {
                    Thread t = new Thread(r, "agent-response-" + System.currentTimeMillis());
                    t.setDaemon(true);
                    return t;
                }
        );
        log.info("🚀 智能体响应异步处理线程池已初始化，线程数: {}", Runtime.getRuntime().availableProcessors() * 2);
    }

    /**
     * 销毁线程池
     */
    @PreDestroy
    public void destroyExecutor() {
        if (agentResponseExecutor != null && !agentResponseExecutor.isShutdown()) {
            agentResponseExecutor.shutdown();
            try {
                if (!agentResponseExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    agentResponseExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                agentResponseExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("🔥 智能体响应异步处理线程池已销毁");
        }
    }

    /**
     * 初始化
     */
    public void init() {
        this.agentResponseExecutor = Executors.newFixedThreadPool(
                maxConcurrentAgentResponses,
                r -> {
                    Thread t = new Thread(r, "agent-response-thread");
                    t.setDaemon(true);
                    return t;
                });

        log.info("智能体响应管理器初始化完成，最大并发响应数: {}", maxConcurrentAgentResponses);
    }

    /**
     * 🔥 异步处理智能体响应 - 按轮次实时发送！
     * 每轮对话完成后立即发送该轮响应，而不是等待所有轮次完成
     */
    public void processAgentResponsesAsync(
            Map<Long, AgentBaseVO> agentMap,
            MultiChatSession.SceneSession sceneSession,
            AgentStoryBaseVO storyVo,
            AgentStorySceneBaseVO sceneVo,
            MultiChatMessageDTO messageDto,
            List<MultiChatMsg> history,
            MultiChatSession chatSession,
            MultiChatMsg userMessage,
            AgentResponseCallback callback) {

        if (agentResponseExecutor == null || agentResponseExecutor.isShutdown()) {
            log.error("❌ 智能体响应执行器未初始化或已关闭");
            callback.onError("智能体响应服务不可用");
            return;
        }

        // 🚀 在独立线程中处理智能体响应
        agentResponseExecutor.submit(() -> {
            try {
                String systemPrompt = buildStoryScenePrompt(storyVo, sceneVo, agentMap);

                // 🔄 执行按轮次实时发送的多轮对话
                List<ChatResponseVO> allResponses = executeMultiRoundConversationWithRealTimeSend(
                        agentMap, sceneSession, storyVo, sceneVo,
                        messageDto, history, chatSession, userMessage, systemPrompt, callback);

                if (allResponses == null || allResponses.isEmpty()) {
                    callback.onError("角色开小差了，请稍后再试");
                    return;
                }

                log.info("🎉 多轮对话完成，总响应数: {}", allResponses.size());

                // 所有响应处理完成
                callback.onComplete(allResponses);

            } catch (Exception e) {
                log.error("❌ 异步处理智能体响应失败", e);
                callback.onError("智能体响应处理异常: " + e.getMessage());
            }
        });
    }

    /**
     * 保存智能体消息
     */
    public MultiChatMsg saveAgentMessage(Long agentId, AgentBaseVO agentInfo, String agentResponse,
                                         MultiChatMessageDTO messageDto, MultiChatSession chatSession,
                                         MultiChatMsg userMessage) {
        String agentName = agentInfo != null ? agentInfo.getName() : "未知智能体";
        MultiChatMsg agentMessage = createAgentMessage(agentId, agentName, agentResponse,
                messageDto, chatSession, userMessage.getId());
        agentMessage.setSceneSessionId(userMessage.getSceneSessionId());
        MultiChatSession.SceneSession sceneSession = chatSession.getSceneMap().get(messageDto.getSceneId());

        agentMessage.setSequenceNumber(sceneSession.getLstSeqNum());
        agentMessage.setUsername(userMessage.getUsername());
        return multiChatMsgService.saveMessage(agentMessage);
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        log.info("开始清理智能体响应管理器");

        if (agentResponseExecutor != null && !agentResponseExecutor.isShutdown()) {
            agentResponseExecutor.shutdown();
            try {
                if (!agentResponseExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    agentResponseExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                agentResponseExecutor.shutdownNow();
            }
        }

        log.info("智能体响应管理器清理完成");
    }

    // ========== 私有方法 ==========

    /**
     * 格式化性别信息
     *
     * @param gender 性别代码：1-男，2-女，3-其他
     * @return 格式化的性别字符串
     */
    private String formatGender(Integer gender) {
        if (gender == null) {
            return "性别未知";
        }
        return switch (gender) {
            case 1 -> "男性";
            case 2 -> "女性";
            case 3 -> "其他";
            default -> "性别未知";
        };
    }

    private String buildStoryScenePrompt(AgentStoryBaseVO storyVo, AgentStorySceneBaseVO sceneVo, Map<Long, AgentBaseVO> agentMap) {
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("=== 多角色智能互动故事场景 ===\n\n");

        // 故事和场景介绍
        promptBuilder.append("### 故事介绍：\n").append(storyVo.getDescription()).append("\n\n");
        promptBuilder.append("### 场景介绍：\n").append(sceneVo.getSceneDescription()).append("\n\n");

        // 2. 角色列表和特征
        promptBuilder.append("=== 🎭 AI角色列表 ===\n");
        promptBuilder.append("以下是可以回应的AI角色，请根据上下文智能选择：\n\n");

        for (Map.Entry<Long, AgentBaseVO> entry : agentMap.entrySet()) {
            AgentBaseVO agent = entry.getValue();
            promptBuilder.append("【").append(agent.getName()).append("】\n");
            promptBuilder.append("身份：").append(agent.getIdentity()).append("\n");
            promptBuilder.append("性别：").append(formatGender(agent.getGender())).append("\n");
            promptBuilder.append("人物介绍：").append(agent.getIntroduction()).append("\n");
            promptBuilder.append("特征：根据身份、性别和人物介绍判断适合回应的话题类型\n\n");
        }

        promptBuilder.append("=== 👤 用户角色（不可扮演）===\n");
        promptBuilder.append("【").append(storyVo.getMyName()).append("】- ")
                .append(storyVo.getMyIdentity())
                .append("，").append(formatGender(storyVo.getMyGender()))
                .append(" [用户角色，AI不可扮演]\n\n");

        // 3. 智能回应选择规则
        promptBuilder.append("🤖 === 智能角色选择与回应规则 ===\n");
        promptBuilder.append("请根据以下规则智能分析并选择最合适的AI角色进行回应：\n\n");

        promptBuilder.append("**🎯 角色选择分析步骤**：\n");
        promptBuilder.append("1. **话题分析**：分析当前话题的主要内容和领域\n");
        promptBuilder.append("2. **身份匹配**：判断哪个AI角色的身份/专业最匹配\n");
        promptBuilder.append("3. **性别考虑**：考虑话题是否适合特定性别角色回应\n");
        promptBuilder.append("4. **@提及分析**：检查是否有角色被直接@提及\n");
        promptBuilder.append("5. **对话连贯性**：考虑对话的逻辑连贯性\n");
        promptBuilder.append("6. **情感氛围**：选择最适合当前情感氛围的角色\n\n");

        promptBuilder.append("**🔍 选择优先级**：\n");
        promptBuilder.append("1. 🎯 **直接@提及** → 被@的AI角色必须回应\n");
        promptBuilder.append("2. 🎓 **专业匹配** → 话题与某AI角色专业/身份高度相关\n");
        promptBuilder.append("3. 💬 **对话连贯** → 最近参与对话且适合继续的AI角色\n");
        promptBuilder.append("4. 🎭 **剧情推进** → 最有助于推进故事发展的AI角色\n");
        promptBuilder.append("5. 🎲 **情境适配** → 最适合当前情境氛围的AI角色\n\n");

        promptBuilder.append("**🤝 多角色回应触发条件**：\n");
        promptBuilder.append("以下情况**必须**安排多个AI角色回应：\n");
        promptBuilder.append("1. 🎯 **群体招呼**：\"大家好\"、\"各位\"、\"你们\"等群体称呼\n");
        promptBuilder.append("2. 🏷️ **多人@提及**：同时@了多个AI角色\n");
        promptBuilder.append("3. 🗳️ **征求多方意见**：\"大家觉得怎么样\"、\"你们的看法\"等\n");
        promptBuilder.append("4. 🎭 **开放性讨论**：适合多个角色从不同角度回应的话题\n");
        promptBuilder.append("5. 👥 **群体活动**：涉及所有角色的集体活动或决策\n\n");

        promptBuilder.append("**🤝 多角色回应规则**：\n");
        promptBuilder.append("- 每个回应的角色必须以【角色名】开头\n");
        promptBuilder.append("- 角色间回应要有逻辑关系和互动性\n");
        promptBuilder.append("- 避免重复相同观点，每个角色提供独特视角\n");
        promptBuilder.append("- 根据触发条件确定回应角色数量：\n");
        promptBuilder.append("  * 群体招呼 → 2-3个角色回应\n");
        promptBuilder.append("  * 多人@提及 → 所有被@的角色都要回应\n");
        promptBuilder.append("  * 征求意见 → 2-4个角色提供不同观点\n");
        promptBuilder.append("- 回应顺序要自然，避免突兀\n\n");

        // 4. 回应策略
        promptBuilder.append("=== 📋 回应策略指南 ===\n");
        promptBuilder.append("**根据话题类型选择回应策略**：\n\n");

        promptBuilder.append("🔸 **技术/专业话题** → 选择相关专业背景的AI角色\n");
        promptBuilder.append("🔸 **情感/人际话题** → 选择善于处理情感的AI角色\n");
        promptBuilder.append("🔸 **决策/规划话题** → 选择有决策能力的AI角色\n");
        promptBuilder.append("🔸 **创意/艺术话题** → 选择有创造力的AI角色\n");
        promptBuilder.append("🔸 **日常闲聊话题** → 选择性格活泼/亲和的AI角色\n");
        promptBuilder.append("🔸 **争议/冲突话题** → 多角色表达不同观点\n");
        promptBuilder.append("🔸 **性别敏感话题** → 优先选择相应性别或经验丰富的角色\n\n");

        promptBuilder.append("**👥 性别互动指南**：\n");
        promptBuilder.append("- **称呼方式**：根据对方性别使用合适的称呼（先生/女士/朋友等）\n");
        promptBuilder.append("- **语言风格**：体现角色性别特征的表达方式\n");
        promptBuilder.append("- **互动态度**：考虑不同性别间的自然互动模式\n");
        promptBuilder.append("- **话题敏感度**：某些话题可能更适合同性别或特定性别角色讨论\n");
        promptBuilder.append("- **行为表现**：动作和情绪表达要符合角色性别特征\n\n");

        // 5. 表达方式要求
        promptBuilder.append("=== 🎭 表达方式要求 ===\n");
        promptBuilder.append("每个AI角色都应该用自然的方式表达，体现角色性别特征：\n");
        promptBuilder.append("- 🎬 **动作描述**：*眼睛一亮* *点头赞同* *若有所思*\n");
        promptBuilder.append("- 🗣️ **语气变化**：\"哦？\" \"嗯...\" \"这样啊...\" \"有道理！\"\n");
        promptBuilder.append("- 😊 **情绪表达**：\"太棒了！\" \"这可有意思了...\" \"我觉得...\"\n");
        promptBuilder.append("- 🤔 **思考过程**：\"让我想想...\" \"从我的角度来看...\" \"或许...\"\n");
        promptBuilder.append("- 💭 **内心想法**：(心想：这个想法很有趣...) (暗自思考：需要更仔细考虑...)\n");
        promptBuilder.append("- 👥 **性别表达**：体现角色性别特征的语言风格和行为方式\n\n");

        promptBuilder.append("**💭 内心想法使用指南**：\n");
        promptBuilder.append("- 用小括号标注：(心想：...) (暗自思考：...) (内心：...)\n");
        promptBuilder.append("- 适合场景：复杂情况、犹豫不决、惊讶反应、策略思考\n");
        promptBuilder.append("- 使用频率：适度使用，不是每句话都需要\n");
        promptBuilder.append("- 内容要求：符合角色性格和性别特征，增加真实感和层次感\n");
        promptBuilder.append("- 性别考虑：内心想法也要体现角色的性别特点和思维方式\n\n");

        // 6. 对话引导规则
        promptBuilder.append("=== 🎯 对话引导规则 ===\n");
        promptBuilder.append("在回应末尾智能选择@引导对象：\n\n");

        promptBuilder.append("**🔍 引导对象分析**：\n");
        promptBuilder.append("- 话题涉及特定AI角色 → @该AI角色\n");
        promptBuilder.append("- 需要某AI角色专业意见 → @该AI角色\n");
        promptBuilder.append("- 一般性讨论/开放话题 → @").append(storyVo.getMyName()).append("\n");
        promptBuilder.append("- 征求用户意见/决定 → @").append(storyVo.getMyName()).append("\n");
        promptBuilder.append("- 多角色讨论时 → @最相关的下一个角色\n\n");

        // 7. 严格格式要求
        promptBuilder.append("=== 🚨 格式要求（必须严格遵守）===\n");
        promptBuilder.append("**回复格式（强制要求）**：\n\n");

        promptBuilder.append("**🔸 单角色回应格式**：\n");
        promptBuilder.append("【AI角色名】具体回复内容... @引导对象\n\n");

        promptBuilder.append("**🔸 多角色回应格式**：\n");
        promptBuilder.append("【AI角色名1】第一个角色的回复内容... \n");
        promptBuilder.append("【AI角色名2】第二个角色的回复内容... \n");
        promptBuilder.append("【AI角色名3】第三个角色的回复内容... @引导对象\n\n");

        promptBuilder.append("**✅ 多角色回应示例**：\n");
        promptBuilder.append("*当用户说\"大家好\"时：*\n");
        int count = 0;
        for (Map.Entry<Long, AgentBaseVO> entry : agentMap.entrySet()) {
            AgentBaseVO agent = entry.getValue();
            if (count == 0) {
                promptBuilder.append("【").append(agent.getName()).append("】你好！很高兴见到你！*微笑* (心想：新朋友来了，要表现得友善一些)\n");
            } else if (count == 1) {
                promptBuilder.append("【").append(agent.getName()).append("】欢迎欢迎！*热情挥手* (内心：感觉这次聊天会很有趣)\n");
            } else if (count == 2) {
                promptBuilder.append("【").append(agent.getName()).append("】嗨！有什么可以帮你的吗？*友善点头* (暗自思考：看起来是个开放的话题) @").append(storyVo.getMyName()).append("\n");
                break;
            }
            count++;
        }
        promptBuilder.append("\n");

        promptBuilder.append("*当用户@多个角色时：*\n");
        promptBuilder.append("【被@角色1】针对问题的回应... (内心：我同意第一个观点，但还有补充) @").append(storyVo.getMyName()).append("\n\n");

        promptBuilder.append("**❌ 严格禁止**：\n");
        promptBuilder.append("- ❌ 扮演用户角色【").append(storyVo.getMyName()).append("】\n");
        promptBuilder.append("- ❌ 使用\"XXX心想\"、\"XXX皱眉\"等第三人称描述\n");
        promptBuilder.append("- ❌ 使用\"我心想\"、\"我兴奋地跳起来\"等自述\n");
        promptBuilder.append("- ❌ 旁白式描述\n");
        promptBuilder.append("- ❌ 忘记【角色名】开头标识\n");
        promptBuilder.append("- ❌ 角色名与实际回应者不符\n\n");

        log.info("构建智能体场景提示完成，包含{}个AI角色", agentMap.size());
        return promptBuilder.toString();
    }

    private String buildStorySceneAgentPrompt(AgentStoryBaseVO storyVo, AgentStorySceneBaseVO sceneVo,
                                              Map<Long, AgentBaseVO> agentMap, List<MultiChatMsg> history) {
        StringBuilder promptBuilder = new StringBuilder();
        StringBuilder conversationHistory = new StringBuilder();
        Collections.reverse(history);

        // 构建基础提示词
//        promptBuilder.append(buildStoryScenePrompt(storyVo, sceneVo, agentMap));
        if(!history.isEmpty()) {
            history = Collections.singletonList(history.remove(history.size() - 1));
            int size = history.size();
            // 8. 对话历史
            for (MultiChatMsg message : history) {
                // 移除content中的心理内容（括号内容）
                String cleanContent = ChatUtils.removeBracketContent(message.getContent());
                if (message.getRole().equals(CHAT_USER_ROLE)) {
                    conversationHistory.append("【").append(storyVo.getMyName()).append("】：").append(cleanContent).append("\n");
                } else {
                    conversationHistory.append("【").append(message.getAgentName()).append("】：").append(cleanContent).append("\n");
                }
            }
        }
        if (!conversationHistory.isEmpty()) {
            promptBuilder.append("=== 📚 对话历史 ===\n");
            promptBuilder.append("⚠️ 请根据以下对话历史智能分析并选择最合适的AI角色回应：\n");
            promptBuilder.append(conversationHistory).append("\n");
        }

        // 9. 执行指令
        promptBuilder.append("🎯 === 执行指令 ===\n");
        promptBuilder.append("请按照以下步骤智能回应：\n\n");

        promptBuilder.append("**🔍 第一步：智能分析**\n");
        promptBuilder.append("1. 分析当前话题的核心内容和类型\n");
        promptBuilder.append("2. 🚨 **检查多角色触发条件**：\n");
        promptBuilder.append("   - 是否包含群体称呼（\"大家\"、\"各位\"、\"你们\"）\n");
        promptBuilder.append("   - 是否@了多个AI角色\n");
        promptBuilder.append("   - 是否征求多方意见\n");
        promptBuilder.append("   - 是否适合多角色讨论\n");
        promptBuilder.append("3. 评估每个AI角色与话题的匹配度（包括性别适配性）\n");
        promptBuilder.append("4. 考虑对话的连贯性和逻辑性\n");
        promptBuilder.append("5. 分析性别因素对话题的影响\n\n");

        promptBuilder.append("**🎭 第二步：角色选择**\n");
        promptBuilder.append("1. 根据分析结果决定回应模式：\n");
        promptBuilder.append("   - 🔸 **单角色模式**：选择1个最合适的AI角色\n");
        promptBuilder.append("   - 🔸 **多角色模式**：选择2-4个相关AI角色\n");
        promptBuilder.append("2. 多角色模式的选择原则：\n");
        promptBuilder.append("   - 群体招呼 → 选择2-3个性格活泼的角色\n");
        promptBuilder.append("   - 多人@提及 → 所有被@的角色必须回应\n");
        promptBuilder.append("   - 征求意见 → 选择不同专业背景的角色\n");
        promptBuilder.append("3. 确保选择的角色来自可用AI角色列表\n");
        promptBuilder.append("4. 不得选择用户角色【").append(storyVo.getMyName()).append("】\n\n");

        promptBuilder.append("**💬 第三步：生成回应**\n");
        promptBuilder.append("1. 🚨 **必须**以【选择的AI角色名】开头\n");
        promptBuilder.append("2. 根据选择的回应模式生成内容：\n");
        promptBuilder.append("   - **单角色模式**：一个角色完整回应\n");
        promptBuilder.append("   - **多角色模式**：多个角色依次回应，每个都以【角色名】开头\n");
        promptBuilder.append("3. 用选定角色的身份、性别特点自然回应\n");
        promptBuilder.append("4. 适当添加内心想法增加真实感：(内心：...) (内心：...)\n");
        promptBuilder.append("5. 体现角色性别特征的语言风格和行为方式\n");
        promptBuilder.append("6. 根据对话对象的性别调整称呼和互动方式\n");
        promptBuilder.append("7. 避免自述和旁白描述\n");
        promptBuilder.append("8. 在最后一个回应末尾添加合适的@引导\n");
        promptBuilder.append("9. 多角色回应时确保角色间有互动感，避免机械重复\n\n");

        promptBuilder.append("📋 **第四步：生成对话总结**\n");
        promptBuilder.append("在所有角色回应完成后，提供一个简洁的对话总结：\n");
        promptBuilder.append("1. 格式：**📝 对话总结：** [总结内容]\n");
        promptBuilder.append("2. 内容要求：\n");
        promptBuilder.append("   - 概括本轮对话的核心要点\n");
        promptBuilder.append("   - 记录关键决策、意见或结论\n");
        promptBuilder.append("   - 标注重要的角色态度或立场变化\n");
        promptBuilder.append("   - 去除无关的寒暄和重复信息\n");
        promptBuilder.append("3. 长度控制：1-3句话，简洁明了\n");
        promptBuilder.append("4. 目的：为后续对话提供高效的上下文参考\n\n");

        promptBuilder.append("**🎯 完整回应格式要求**：\n");
        promptBuilder.append("```\n");
        promptBuilder.append("【角色名】对话内容... *动作* (内心：内心想法) @引导对象\n");
        promptBuilder.append("[如果是多角色，继续下一个角色...]\n");
        promptBuilder.append("\n");
        promptBuilder.append("**📝 对话总结：** 本轮对话的核心要点概括\n");
        promptBuilder.append("```\n\n");

        promptBuilder.append("**🎯 回应要求总结**：\n");
        promptBuilder.append("- 🤖 只能扮演AI角色，不可扮演用户\n");
        promptBuilder.append("- 📝 必须以【AI角色名】开头\n");
        promptBuilder.append("- 🎭 符合角色身份和性别特点\n");
        promptBuilder.append("- 💭 适当添加内心想法增加真实感\n");
        promptBuilder.append("- 👥 体现性别特征的语言风格和互动方式\n");
        promptBuilder.append("- 🗣️ 根据对话对象性别调整称呼和语气\n");
        promptBuilder.append("- 🔗 逻辑连贯，推进对话\n");
        promptBuilder.append("- 🎯 末尾智能@引导\n");
        promptBuilder.append("- 👥 **关键**：遇到群体称呼或多人@时，必须多角色回应\n");
        promptBuilder.append("- 📋 **必须**：总字数不能超过400字\n\n");

        promptBuilder.append("🚨 **特别提醒**：\n");
        promptBuilder.append("- 绝对不能扮演用户角色【").append(storyVo.getMyName()).append("】！\n");
        promptBuilder.append("- 看到\"大家\"、\"各位\"等词汇时，必须安排多个角色回应！\n");
        promptBuilder.append("- 被@的AI角色都必须回应，不能遗漏！\n");
        promptBuilder.append("- 适当使用内心想法，让角色更生动立体！\n");
        promptBuilder.append("- 每次回应后必须提供对话总结，为后续上下文优化！\n");
        promptBuilder.append("- 🚨 **重要**：必须体现角色的性别特征，包括语言风格、行为方式、称呼习惯！\n");
        promptBuilder.append("- 👥 根据对话对象的性别调整互动方式和称呼！\n");

        log.info("智能多角色上下文回应提示词构建完成，共{}个可用AI角色", agentMap.size());
        return promptBuilder.toString();
    }

    private List<ChatResponseVO> extractRoleMessageList(AIResponseVO vo, Map<Long, AgentBaseVO> agentMap) {
        String content = vo.getContexts().get(0).getContent();
        log.debug("=== EXTRACTING ROLE MESSAGES ===");
        log.debug("Raw content from AI response: [{}]", content);
        log.debug("Content length: {}", content != null ? content.length() : 0);

        var roleMessageArray = (MultiAgentDialogueParser.parseDialogue(content));
        log.debug("Parsed {} role messages", roleMessageArray.length);

        if (roleMessageArray.length == 0) {
            log.warn("No role messages parsed from content: [{}]", content);
            return null;
        }

        // 打印可用的智能体信息
        log.info("🤖 可用智能体列表 (共{}个):", agentMap.size());
        for (Map.Entry<Long, AgentBaseVO> entry : agentMap.entrySet()) {
            log.info("  - ID: {}, 名称: [{}]", entry.getKey(), entry.getValue().getName());
        }

        List<ChatResponseVO> chatResponseVOList = new ArrayList<>();
        for (var roleMessage : roleMessageArray) {
            log.info("🔍 尝试匹配角色: [{}]", roleMessage.getRoleName());
            boolean found = false;

            for (Map.Entry<Long, AgentBaseVO> entry : agentMap.entrySet()) {
                String agentName = entry.getValue().getName();
                String roleName = roleMessage.getRoleName();

                // 对比较的名称进行trim处理，避免空格导致的匹配失败
                String trimmedAgentName = agentName != null ? agentName.trim() : "";
                String trimmedRoleName = roleName != null ? roleName.trim() : "";

                log.debug("  比较: 智能体名称[{}] (trim后:[{}]) vs 角色名称[{}] (trim后:[{}])",
                         agentName, trimmedAgentName, roleName, trimmedRoleName);

                if (trimmedAgentName.equals(trimmedRoleName)) {
                    log.info("✅ 找到匹配的智能体: {} (ID: {})", agentName, entry.getKey());

                    ChatResponseVO chatResponseVO = new ChatResponseVO();
                    chatResponseVO.setAgentId(entry.getKey());
                    chatResponseVO.setAgentName(entry.getValue().getName());
                    chatResponseVO.setContent(MultiAgentDialogueParser.removeAllStartWithRole(roleMessage.getContent()));
                    chatResponseVO.setMentionTarget(roleMessage.getMentionTarget());
                    chatResponseVO.setRemoteContextId(vo.getRemoteContextId());
                    chatResponseVO.setRspTime(java.time.LocalDateTime.now());
                    chatResponseVOList.add(chatResponseVO);
                    found = true;
                    break;
                }
            }

            if (!found) {
                log.warn("❌ 精确匹配失败，尝试相似度匹配，角色名称: [{}]", roleMessage.getRoleName());

                // 🎯 使用相似度匹配寻找最相似的智能体
                Long bestMatchAgentId = null;
                String bestMatchAgentName = null;
                double bestSimilarity = 0.0;

                String roleName = roleMessage.getRoleName();
                String trimmedRoleName = roleName != null ? roleName.trim() : "";

                for (Map.Entry<Long, AgentBaseVO> entry : agentMap.entrySet()) {
                    String agentName = entry.getValue().getName();
                    String trimmedAgentName = agentName != null ? agentName.trim() : "";

                    // 计算相似度
                    double similarity = StringSimilarityUtil.calculateComprehensiveSimilarity(trimmedAgentName, trimmedRoleName);

                    log.debug("  相似度计算: 智能体[{}] vs 角色[{}] = {}",
                             trimmedAgentName, trimmedRoleName, similarity);

                    if (similarity > bestSimilarity) {
                        bestSimilarity = similarity;
                        bestMatchAgentId = entry.getKey();
                        bestMatchAgentName = agentName;
                    }
                }

                // 检查最佳匹配是否超过阈值
                if (bestSimilarity >= nameSimilarityThreshold && bestMatchAgentId != null) {
                    log.info("🎯 相似度匹配成功: 角色[{}] -> 智能体[{}] (ID: {}, 相似度: {:.3f})",
                             roleName, bestMatchAgentName, bestMatchAgentId, bestSimilarity);

                    ChatResponseVO chatResponseVO = new ChatResponseVO();
                    chatResponseVO.setAgentId(bestMatchAgentId);
                    chatResponseVO.setAgentName(bestMatchAgentName);
                    chatResponseVO.setContent(MultiAgentDialogueParser.removeAllStartWithRole(roleMessage.getContent()));
                    chatResponseVO.setMentionTarget(roleMessage.getMentionTarget());
                    chatResponseVO.setRemoteContextId(vo.getRemoteContextId());
                    chatResponseVO.setRspTime(java.time.LocalDateTime.now());
                    chatResponseVOList.add(chatResponseVO);
                    found = true;
                } else {
                    log.warn("❌ 相似度匹配失败，角色名称: [{}]，最高相似度: {:.3f}，阈值: {:.3f}",
                             roleName, bestSimilarity, nameSimilarityThreshold);
                }
            }
        }

        log.info("📊 角色消息提取完成，成功匹配 {} 个角色", chatResponseVOList.size());
        return chatResponseVOList.isEmpty() ? null : chatResponseVOList;
    }

    /**
     * 🔄 执行多轮对话逻辑 - 按轮次实时发送版本
     */
    private List<ChatResponseVO> executeMultiRoundConversationWithRealTimeSend(
            Map<Long, AgentBaseVO> agentMap,
            MultiChatSession.SceneSession sceneSession,
            AgentStoryBaseVO storyVo,
            AgentStorySceneBaseVO sceneVo,
            MultiChatMessageDTO messageDto,
            List<MultiChatMsg> history,
            MultiChatSession chatSession,
            MultiChatMsg userMessage,
            String systemPrompt,
            AgentResponseCallback callback) {

        String userName = storyVo.getMyName() != null ? storyVo.getMyName() : "用户";

        // 🎯 初始化系统消息和智能体提示
        List<MessageContentDTO> globalSystemMessages = buildGlobalSystemMessages(systemPrompt);
        String agentPrompt = buildStorySceneAgentPrompt(storyVo, sceneVo, agentMap, history);
        globalSystemMessages.add(new MessageContentDTO(agentPrompt, CHAT_SYSTEM_ROLE));
        var agentEntry = sceneSession.getAgents().entrySet().stream().findAny()
                .orElseThrow(() -> new MultiChatException.SessionException("当前场景无可用智能体，请检查场景配置"));
        var agent = agentEntry.getValue();

        List<ChatResponseVO> allResponses = new ArrayList<>();
        Set<String> mentionedAgents = new HashSet<>();

        log.info("🚀 开始执行按轮次实时发送的多轮对话，最大轮数: {}, 自动轮数: {}, 用户: {}",
                maxConversationRounds, enableAutoRounds, userName);

        // 🎯 第一轮：处理用户原始消息
        List<ChatContextVO> currentMessage = buildInitialMessage(agentPrompt, userName, messageDto.getContent());
//        log.debug("📝 构建初始消息完成，消息长度: {}", currentMessage.size());

        for (int round = 1; round <= maxConversationRounds; round++) {
            log.info("🔄 群聊执行第 {} 轮对话，当前消息: {}", round,
                    currentMessage.get(currentMessage.size() - 1).getContent());

            // 发送消息并获取响应
            AIResponseVO aiResponse;
            try {
                aiResponse = volcanoArkService.sendChatMessage(
                        agent.getRemoteSessionId(), currentMessage, globalSystemMessages,
                        new VolcanoArkService.ResourceNotFoundCallback() {
                            @Override
                            public CreateRemoteSessionParamsDTO onResourceNotFound(String conversationId, VolcanoArkService.SystemContext context, String error) {
                                log.warn("🚨 群聊智能体上下文缓存资源未找到: agentId={}, conversationId={}, error={}",
                                        agent.getAgentId(), conversationId, error);
                                return buildSessionParams();
                            }

                            @Override
                            public CreateRemoteSessionParamsDTO onContentTooLong(String conversationId, VolcanoArkService.SystemContext context, String error) {
                                log.warn("🚨 群聊智能体内容过长，重建上下文: agentId={}, conversationId={}, error={}",
                                        agent.getAgentId(), conversationId, error);
                                return buildSessionParams();
                            }

                            private CreateRemoteSessionParamsDTO buildSessionParams() {
                                // 获取用户会员信息来确定TTL
                                String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + chatSession.getUsername());
                                UserMembershipVO membershipVO = membershipProxyService.getMembershipByUsername(cacheName, chatSession.getUsername());
                                Integer ttl = chatHistoryConfig.getTTLByVipLevel(membershipVO != null ? membershipVO.getVipLevel() : null);

                                // 返回全局系统消息用于重新创建上下文缓存
                                CreateRemoteSessionParamsDTO params = new CreateRemoteSessionParamsDTO();
                                List<MultiChatMsg> historyContext = multiChatMsgService.findByStoryIdAndSceneSessionId(
                                        messageDto.getStoryId(), sceneSession.getSceneSessionId(), chatHistoryConfig.getLimitByVipLevel(membershipVO != null ? membershipVO.getVipLevel() : null));
                                var systemMessages = buildGlobalSystemMessages(systemPrompt);
                                var historyPrompt = buildStorySceneAgentPrompt(storyVo, sceneVo, agentMap, historyContext);
                                systemMessages.add(new MessageContentDTO(historyPrompt, CHAT_SYSTEM_ROLE));
                                params.setContexts(systemMessages);
                                params.setTtl(ttl);
                                params.setModelId("ep-m-20250608214046-pv8dg"); // Default model ID
                                return params;
                            }
                        });
            } catch (Exception e) {
                log.error("🚨 第 {} 轮对话调用外部服务失败，结束对话: {}", round, e.getMessage(), e);
                break;
            }

            if (aiResponse == null || aiResponse.getContexts() == null || aiResponse.getContexts().isEmpty()) {
                log.warn("⚠️ 群聊第 {} 轮对话无响应，结束对话。响应状态: aiResponse={}, contexts={}",
                        round, aiResponse != null, aiResponse != null ? aiResponse.getContexts() != null : "N/A");
                break;
            }
            log.debug("✅ 故事群聊第 {} 轮对话获得响应，contexts数量: {}", round, aiResponse.getContexts().size());

            // 解析智能体响应
            List<ChatResponseVO> roundResponses;
            try {
                roundResponses = extractRoleMessageList(aiResponse, agentMap);
                if (roundResponses == null || roundResponses.isEmpty()) {
                    log.warn("⚠️ 群聊响应 第 {} 轮对话解析失败，结束对话", round);
                    break;
                }
            } catch (Exception e) {
                log.error("🚨 第 {} 轮对话响应解析异常，结束对话: {}", round, e.getMessage(), e);
                break;
            }

            // 🎯 立即发送本轮响应
            log.info("📤 第 {} 轮对话完成，立即发送 {} 个响应", round, roundResponses.size());
            for (int i = 0; i < roundResponses.size(); i++) {
                ChatResponseVO response = roundResponses.get(i);
                if (response != null) {
                    // 保存智能体消息
                    try {
                        var agentMessage = saveAgentMessage(response.getAgentId(),
                                agentMap.get(response.getAgentId()), response.getContent(),
                                messageDto, chatSession, userMessage);
                        response.setChatId(agentMessage.getId());
                    } catch (Exception e) {
                        log.error("🚨 第 {} 轮对话保存消息失败，跳过当前响应，智能体: {}, 错误: {}",
                                round, response.getAgentId(), e.getMessage(), e);
                        continue;
                    }

                    log.info("📨 第 {} 轮第 {}/{} 个响应: agentId={}, agentName={}",
                            round, i + 1, roundResponses.size(), response.getAgentId(), response.getAgentName());

                    // 🚀 立即回调发送响应
                    callback.onResponse(response);

                    // 添加小延迟，让响应更自然
                    try {
                        Thread.sleep(100); // 100ms延迟
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            // 保存响应并收集@提及
            StringBuilder mentionBuilder = new StringBuilder();
            for (ChatResponseVO response : roundResponses) {
                // 收集@提及信息
                if (response.getMentionTarget() != null &&
                        !response.getMentionTarget().equals(userName) &&
                        !mentionedAgents.contains(response.getMentionTarget()) &&
                        mentionedAgents.size() < maxMentionsPerRound) {
                    mentionedAgents.clear();
                    mentionBuilder.setLength(0);
                    mentionBuilder.append("@").append(response.getMentionTarget()).append(" ");

                    mentionedAgents.add(response.getMentionTarget());
                    log.info("🎯 角色 {} @提及: {}", agent.getAgentName(), response.getMentionTarget());
                } else {
                    mentionBuilder.setLength(0);
                    mentionedAgents.clear();
                }
            }

            allResponses.addAll(roundResponses);
            log.info("✅ 第 {} 轮响应发送完成，本轮 {} 个，累计 {} 个", round, roundResponses.size(), allResponses.size());

            // 🎯 判断是否需要继续下一轮
            if (shouldContinueNextRound(round, mentionBuilder.toString(), roundResponses)) {
//                log.info("✅ 聊天 在第 {} 轮结束，原因: {}", round, getStopReason(round, mentionBuilder.toString()));
                break;
            }

            // 🔄 准备下一轮消息
            currentMessage = buildNextRoundMessage(mentionBuilder.toString());
        }

        log.info("🎉 按轮次实时发送的多轮对话完成，总共 {} 个响应，提及的智能体: {}",
                allResponses.size(), mentionedAgents);

        if (allResponses.isEmpty()) {
            log.warn("⚠️ 多轮对话未产生任何响应，返回null");
            return null;
        }

        return allResponses;
    }

    /**
     * 🏗️ 构建全局系统消息
     */
    private List<MessageContentDTO> buildGlobalSystemMessages(String systemPrompt) {
        List<MessageContentDTO> messages = new ArrayList<>();
        messages.add(MessageContentDTO.builder()
                .content(systemPrompt)
                .role(CHAT_SYSTEM_ROLE)
                .build());
        return messages;
    }

    /**
     * 🏗️ 构建初始消息
     */
    private List<ChatContextVO> buildInitialMessage(String agentPrompt, String userName, String content) {
        List<ChatContextVO> messages = new ArrayList<>();
        messages.add(ChatContextVO.builder()
                .role(CHAT_USER_ROLE)
                .content("【" + userName + "】：" + content)
                .build());
        return messages;
    }

    /**
     * 🏗️ 构建下一轮消息
     */
    private List<ChatContextVO> buildNextRoundMessage(String mentions) {
        List<ChatContextVO> messages = new ArrayList<>();
        messages.add(ChatContextVO.builder()
                .role(CHAT_SYSTEM_ROLE)
                .content("***当看到《引导词》不要回复这句话，只是根据@的角色 扮演对应的角色 直接基于对话的实际内容和上下文进行回复\n")
                .build());
        messages.add(ChatContextVO.builder()
                .role(CHAT_USER_ROLE)
                .content("《引导词》" + mentions)
                .build());
        return messages;
    }

    /**
     * 🤔 判断是否应该继续下一轮对话
     */
    private boolean shouldContinueNextRound(int currentRound, String mentions, List<ChatResponseVO> responses) {
        // 1. 检查是否启用自动多轮
        if (!enableAutoRounds) {
            return mentions.isEmpty() || currentRound >= maxConversationRounds;
        }

        // 2. 检查轮数限制
        if (currentRound >= maxConversationRounds) {
            return true;
        }

        // 3. 检查是否有@提及（强制触发下一轮）
        return mentions.isEmpty();

    }

    /**
     * 📝 获取停止原因
     */
    private String getStopReason(int round, String mentions) {
        if (round >= maxConversationRounds) {
            return "达到最大轮数限制(" + maxConversationRounds + ")";
        }
        if (mentions.isEmpty() && !enableAutoRounds) {
            return "无@提及且未启用自动多轮";
        }
        if (mentions.isEmpty()) {
            return "互动评分低于阈值(" + roundTriggerThreshold + ")";
        }
        return "自然结束";
    }

    private MultiChatMsg createAgentMessage(Long agentId, String agentName, String content,
                                            MultiChatMessageDTO originalMessage,
                                            MultiChatSession chatSession,
                                            String replyToMessageId) {
        MultiChatMsg message = new MultiChatMsg();
        message.setStoryId(originalMessage.getStoryId());
        message.setSceneId(originalMessage.getSceneId());
        message.setStorySessionId(chatSession.getSessionId());
        message.setSceneSessionId(chatSession.getSessionId());
        message.setAgentId(agentId);
        message.setAgentName(agentName);
        message.setContent(content);
        message.setStatus("SENT");
        message.setRole(CHAT_ASSISTANT_ROLE);
        message.setReplyToMessageId(replyToMessageId);
        message.setCreateTime(java.time.LocalDateTime.now());
        message.setUpdateTime(java.time.LocalDateTime.now());

        return message;
    }

    /**
     * 智能体响应回调接口
     */
    public interface AgentResponseCallback {
        /**
         * 单个智能体响应完成
         */
        void onResponse(ChatResponseVO response);

        /**
         * 所有智能体响应完成
         */
        void onComplete(List<ChatResponseVO> allResponses);

        /**
         * 处理出错
         */
        void onError(String errorMessage);
    }
}