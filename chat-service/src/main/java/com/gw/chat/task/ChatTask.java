package com.gw.chat.task;

import com.coze.openapi.client.exception.CozeApiException;
import com.gw.chat.config.CacheProperties;
import com.gw.chat.config.ChatHistoryConfig;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.dto.ChatMessageDto;
import com.gw.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.chat.dto.MessageContentDTO;
import com.gw.chat.entity.ChatCompressMessage;
import com.gw.chat.entity.ChatMessage;
import com.gw.chat.entity.ConversationSession;
import com.gw.chat.exception.VolcanoContentParsingException;
import com.gw.chat.service.*;
import com.gw.chat.utils.ChatUtils;
import com.gw.chat.vo.AIResponseVO;
import com.gw.chat.vo.ChatContextVO;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.MyAgentSettingVO;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.common.membership.constant.MemberBenefitConstant;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.common.util.AiUtils;
import com.volcengine.ark.runtime.model.Usage;
import com.gw.chat.service.AgentRemoteVolvanoService.SystemContext;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.gw.chat.constant.ChatConstant.CHAT_ASSISTANT_ROLE;
import static com.gw.chat.constant.ChatConstant.CHAT_SYSTEM_ROLE;

/**
 * 聊天任务处理类
 * 负责异步处理聊天消息和AI回复
 */
@Component
@Log4j2
public class ChatTask {

    // ==================== 常量定义 ====================
    private static final String AI_SERVICE_ERROR_MESSAGE = "抱歉，AI服务暂时无法响应，请稍后再试。";
    private static final String AI_BUSY_MESSAGE = "AI服务正在处理其他请求，请稍后再试。";
    private static final String AI_EXCEPTION_MESSAGE = "AI调用异常。";
    private static final String AI_VOLCANO_MESSAGE = "角色开小差。请重试";
    private static final int DEFAULT_HISTORY_LIMIT = 60;

    // ==================== 依赖注入 ====================
    private final AgentProxyService agentProxyService;
    private final ChatMessageService chatMessageService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final WebSocketService webSocketService;
    private final AgentCozeRemoteService agentCozeRemoteService;
    private final AgentRemoteVolvanoService agentRemoteVolvanoService;
    private final MembershipProxyService membershipProxyService;
    private final MongoTemplate mongoTemplate;
    private final CacheProperties cacheProperties;
    private final ChatCompressMessageService chatCompressMessageService;
    private final ChatHistoryConfig chatHistoryConfig;

    // ==================== 构造函数 ====================
    public ChatTask(AgentProxyService agentProxyService,
                    ChatMessageService chatMessageService,
                    RedisTemplate<String, Object> redisTemplate,
                    ChatCompressMessageService chatCompressMessageService,
                    WebSocketService webSocketService,
                    AgentCozeRemoteService agentCozeRemoteService,
                    AgentRemoteVolvanoService agentRemoteVolvanoService,
                    MembershipProxyService membershipProxyService,
                    MongoTemplate mongoTemplate,
                    CacheProperties cacheProperties,
                    ChatHistoryConfig chatHistoryConfig) {
        this.agentProxyService = agentProxyService;
        this.chatMessageService = chatMessageService;
        this.redisTemplate = redisTemplate;
        this.webSocketService = webSocketService;
        this.agentCozeRemoteService = agentCozeRemoteService;
        this.agentRemoteVolvanoService = agentRemoteVolvanoService;
        this.membershipProxyService = membershipProxyService;
        this.mongoTemplate = mongoTemplate;
        this.cacheProperties = cacheProperties;
        this.chatCompressMessageService = chatCompressMessageService;
        this.chatHistoryConfig = chatHistoryConfig;
    }

    // ==================== 主要方法 ====================

    /**
     * 异步发送消息并获取AI回复
     *
     * @param messageDto 用户消息DTO
     * @param username   用户名
     * @return 包含AI回复的CompletableFuture
     */
    @Async("aiProcessorExecutor")
    public CompletableFuture<ChatMessageDto> sendMessageAsync(ChatMessageDto messageDto, String username) {
        return CompletableFuture.supplyAsync(() -> handleChatMessage(messageDto, username));
    }

    /**
     * 处理聊天消息的核心方法
     */
    private ChatMessageDto handleChatMessage(ChatMessageDto userMessage, String username) {
        log.info("开始处理聊天消息 - 用户: {}, 会话: {}, 智能体: {}, 消息ID: {}",
                username, userMessage.getSessionId(), userMessage.getAgentId(), userMessage.getId());

        try {
            // 1. 验证会话和获取设置
            ConversationSession session = validateSession(userMessage.getSessionId(), username);
            MyAgentSettingVO setting = agentProxyService.getMyAgentSetting(username, userMessage.getAgentId());
            List<ChatContextVO> msgList = new ArrayList<>();
            if(userMessage.isReAnswer()){
                msgList.add(ChatContextVO.builder()
                        .role(CHAT_SYSTEM_ROLE)
                        .type("text")
                        .content("### 针对下面用户的问题，请再试一次，不要参考你之前给出的任何回答。我想听一个完全不同的解释")
                        .contentType("text")
                        .build());
            }
            // 2. 构建聊天历史
            List<ChatContextVO> history = buildChatHistory(userMessage);
            log.debug("聊天历史构建完成 - 会话: {}, 历史消息数量: {}", userMessage.getSessionId(), history.size());
            msgList.addAll( history);
            if(userMessage.isReAnswer()){
                log.info("开始处理重新回答 - 上下文 智能体设置: {}",msgList);
            }
            // 3. 调用AI服务
            AIResponseVO aiResponse = callAIService(userMessage, session, msgList, setting);

            // 4. 更新远程会话ID（如果需要）
            updateRemoteConversationId(session, aiResponse);

            // 5. 构建并保存AI消息
            ChatMessage aiMessage = buildAIMessage(userMessage, session, aiResponse);
            ChatMessage savedMessage = saveAIMessage(userMessage, aiMessage);

            // 6. 更新会话最后消息
            session.setLstSeqNum(aiMessage.getSeqNum());
            updateSessionLastMessage(session, savedMessage);
            if (session.getRefreshSetting()) {
                session.setMySetting(setting);
            }

            // 7. 通知用户和记录使用量
            ChatMessageDto responseDto = ChatMessageDto.fromEntity(savedMessage);
            responseDto.setReAnswer(userMessage.isReAnswer());

            notifyUser(userMessage, responseDto);
            recordUserUsage(username);

            log.info("AI消息处理完成 - 用户: {}, 消息ID: {}", username, savedMessage.getId());
            return responseDto;

        }catch (VolcanoContentParsingException e) {
            ConversationSession session = validateSession(userMessage.getSessionId(), username);
            session.setRemoteConversationId("error");
            mongoTemplate.save(session);
            log.error("需要重新更新远程会话ID{}",e.getMessage(),e );
            return handleVolcanoContentParsingException(e, userMessage, username);
        }
        catch (RuntimeException e) {
            return handleRuntimeException(e, userMessage, username);
        } catch (Exception e) {
            return handleGenericException(e, userMessage, username);
        }
    }

    /**
     * 调用AI服务
     */
    private AIResponseVO callAIService(ChatMessageDto message, ConversationSession session,
                                       List<ChatContextVO> history, MyAgentSettingVO setting) throws Exception {
        String voiceId = extractVoiceId(message);
        message.getAgent().setPlatform(AgentConstant.HUO_SHAN_PLATFORM);
        return switch (message.getAgent().getPlatform()) {
            case AgentConstant.COZE_PLATFORM ->
                    sendCozeMessage(session, message.getAgent().getRemoteBotId(), history, voiceId, setting);
            case AgentConstant.HUO_SHAN_PLATFORM ->
                    sendVolcanoArkMessage(session, history, voiceId, setting, message.getAgent());
            default -> throw new RuntimeException("Unsupported platform: " + message.getAgent().getPlatform());
        };
    }

    /**
     * 发送Coze平台消息
     */
    private AIResponseVO sendCozeMessage(ConversationSession session, String botId, List<ChatContextVO> history,
                                         String voiceId, MyAgentSettingVO setting) throws Exception {
        Map<String, String> customVariables = buildCustomVariables(setting);
        return agentCozeRemoteService.sendChatMessage(
                session.getRemoteConversationId(),
                botId,
                session.getUsername(),
                history,
                customVariables,
                setting != null && setting.getAutoPlayVoice() != null ? setting.getAutoPlayVoice() : false,
                voiceId);
    }

    /**
     * 发送火山方舟平台消息
     */
    private AIResponseVO sendVolcanoArkMessage(ConversationSession session, List<ChatContextVO> history,
                                               String voiceId, MyAgentSettingVO setting, Object agent) throws Exception {
        return agentRemoteVolvanoService.sendChatMessage(
                session.getRemoteConversationId(),
                history,
                setting != null && setting.getAutoPlayVoice() != null ? setting.getAutoPlayVoice() : false,
                voiceId, new AgentRemoteVolvanoService.VolcanoChatCallback() {
                    @Override
                    public void onChatFinish(Usage usage) {
                        // 处理聊天完成回调，记录token使用情况
                        if (usage != null) {
                            log.info("聊天完成 - 会话: {}, Token使用情况: prompt={}, completion={}, total={}",
                                    session.getId(),
                                    usage.getPromptTokens(),
                                    usage.getCompletionTokens(),
                                    usage.getTotalTokens());

                            // 如果token使用量过大，标记需要重建上下文
                            if (usage.getPromptTokens() > 30000) {
                                log.warn("token使用量过大，下一轮对话需要重建上下文：{}", usage);
                                session.setRemoteConversationId("error");
                            }
                        }
                    }

                    @Override
                    public CreateRemoteSessionParamsDTO onResourceNotFound(String conversationId, SystemContext systemContext, String errorDetails) {
                        String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + session.getUsername());

                        UserMembershipVO membershipVO = membershipProxyService.getMembershipByUsername(cacheName, session.getUsername());
                        List<MessageContentDTO> contexts = buildSystemContext(agent, setting, session, membershipVO);

                        CreateRemoteSessionParamsDTO params = new CreateRemoteSessionParamsDTO();
                        params.setContexts(contexts);
                        params.setTtl(chatHistoryConfig.getTTLByVipLevel(membershipVO != null ? membershipVO.getVipLevel() : null));
                        params.setModelId("ep-m-20250608214046-pv8dg"); // Default model ID

                        return params;
                    }

                    @Override
                    public CreateRemoteSessionParamsDTO onContentTooLong(String conversationId, SystemContext systemContext, String errorDetails) {
                        log.info("内容过长，重建上下文 - 会话: {}, 用户: {}", conversationId, session.getUsername());

                        String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + session.getUsername());

                        UserMembershipVO membershipVO = membershipProxyService.getMembershipByUsername(cacheName, session.getUsername());
                        List<MessageContentDTO> contexts = buildSystemContext(agent, setting, session, membershipVO);

                        CreateRemoteSessionParamsDTO params = new CreateRemoteSessionParamsDTO();
                        params.setContexts(contexts);
                        params.setTtl(chatHistoryConfig.getTTLByVipLevel(membershipVO != null ? membershipVO.getVipLevel() : null));
                        params.setModelId("ep-m-20250608214046-pv8dg"); // Default model ID

                        return params;
                    }
                });
    }

    // ==================== 异常处理方法 ====================

    /**
     * 处理运行时异常
     */
    private ChatMessageDto handleRuntimeException(RuntimeException e, ChatMessageDto message, String username) {
        log.error("处理RuntimeException异常 - 用户: {}, 会话: {}, 消息ID: {}, 异常类型: {}",
                username, message.getSessionId(), message.getId(), e.getClass().getSimpleName());

        // 特殊处理Coze API异常
        if (e.getCause() instanceof CozeApiException cozeException) {
            return handleCozeApiException(cozeException, message, username);
        }

        log.error("未处理的RuntimeException - 用户: {}, 会话: {}", username, message.getSessionId(), e);
        throw new RuntimeException("AI服务出现异常，请稍后重试");
    }

    /**
     * 处理Coze API异常
     */
    private ChatMessageDto handleCozeApiException(CozeApiException cozeException, ChatMessageDto message, String username) {
        log.error("CozeApi异常处理 - 错误码: {}, 错误信息: {}, 会话: {}",
                cozeException.getCode(), cozeException.getMsg(), message.getSessionId());
        log.error("CozeApi服务调用失败详情", cozeException);

        ChatMessage errorMessage = buildErrorMessage(message, username, AI_SERVICE_ERROR_MESSAGE);

        // 特殊处理会话占用错误
        if (cozeException.getCode() == 4016) {
            log.warn("会话占用错误 - 会话: {}, 设置为BUSY状态", message.getSessionId());
            errorMessage.setStatus(ChatMessage.MessageStatus.BUSY);
            errorMessage.setContent(AI_BUSY_MESSAGE);
        }

        ChatMessageDto errorDto = ChatMessageDto.fromEntity(errorMessage);
        log.info("开始发送CozeApi错误消息到客户端 - 用户: {}, 错误码: {}", username, cozeException.getCode());
        notifyUser(message, errorDto);
        log.info("CozeApi错误消息发送完成 - 用户: {}, 会话: {}", username, message.getSessionId());

        return errorDto;
    }
    private ChatMessageDto handleVolcanoContentParsingException(Exception e, ChatMessageDto message, String username) {

        log.error("异步调用AI服务失败详情", e);

        ChatMessage errorMessage = buildErrorMessage(message, username, AI_VOLCANO_MESSAGE);
        errorMessage.setStatus(ChatMessage.MessageStatus.FAILED);

        ChatMessageDto errorDto = ChatMessageDto.fromEntity(errorMessage);
        log.info("开始发送通用错误消息到客户端 - 用户: {}, 异常: {}", username, e.getClass().getSimpleName());
        notifyUser(message, errorDto);
        log.info("通用错误消息发送完成 - 用户: {}, 会话: {}", username, message.getSessionId());

        return errorDto;
    }
    /**
     * 处理通用异常
     */
    private ChatMessageDto handleGenericException(Exception e, ChatMessageDto message, String username) {
        log.error("处理通用异常 - 用户: {}, 会话: {}, 消息ID: {}, 异常类型: {}",
                username, message.getSessionId(), message.getId(), e.getClass().getSimpleName());
        log.error("异步调用AI服务失败详情", e);

        ChatMessage errorMessage = buildErrorMessage(message, username, AI_EXCEPTION_MESSAGE);
        errorMessage.setStatus(ChatMessage.MessageStatus.FAILED);

        ChatMessageDto errorDto = ChatMessageDto.fromEntity(errorMessage);
        log.info("开始发送通用错误消息到客户端 - 用户: {}, 异常: {}", username, e.getClass().getSimpleName());
        notifyUser(message, errorDto);
        log.info("通用错误消息发送完成 - 用户: {}, 会话: {}", username, message.getSessionId());

        return errorDto;
    }

    /**
     * 构建错误消息
     */
    private ChatMessage buildErrorMessage(ChatMessageDto message, String username, String content) {
        return ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(message.getSessionId())
                .username(username)
                .agentId(getAgentIdFromSession(message.getSessionId()))
                .role(CHAT_ASSISTANT_ROLE)
                .content(content)
                .status(ChatMessage.MessageStatus.FAILED)
                .createdAt(LocalDateTime.now())
                .build();
    }

    // ==================== 辅助方法 ====================

    /**
     * 通知用户
     */
    private void notifyUser(ChatMessageDto originalMessage, ChatMessageDto responseDto) {
        try {
            webSocketService.sendMessageToUser(originalMessage.getSender(), responseDto);
            log.debug("用户通知发送成功 - 用户: {}, 消息ID: {}",
                    originalMessage.getSender(), responseDto.getId());
        } catch (Exception e) {
            log.error("发送用户通知失败 - 用户: {}, 消息ID: {}",
                    originalMessage.getSender(), responseDto.getId(), e);
        }
    }

    /**
     * 记录用户使用量
     */
    private void recordUserUsage(String username) {
        try {
            membershipProxyService.asyncRecordBenefitUsage(username, MemberBenefitConstant.DAILY_LIMITED_CHAT_CODE, 1);
            log.debug("用户使用量记录完成 - 用户: {}", username);
        } catch (Exception e) {
            log.warn("记录用户使用量失败 - 用户: {}, 错误: {}", username, e.getMessage());
        }
    }

    /**
     * 验证会话
     */
    private ConversationSession validateSession(String sessionId, String username) {
        Query query = new Query(Criteria.where("id").is(sessionId)
                .and("username").is(username)
                .and("status").is(ConversationSession.SessionStatus.ACTIVE));
        ConversationSession session = mongoTemplate.findOne(query, ConversationSession.class);

        if (session == null) {
            throw new EntityNotFoundException("会话不存在或已失效");
        }
        return session;
    }

    /**
     * 构建聊天历史
     */
    private List<ChatContextVO> buildChatHistory(ChatMessageDto message) {
        try {
            // 简化版本：直接从数据库获取历史消息
            List<ChatMessage> sessionHistory;
            if(message.getAgent().getPlatform().equals(AgentConstant.HUO_SHAN_PLATFORM)){
                sessionHistory = getSessionHistory(message.getSessionId(), 1);
            }else {
                sessionHistory = getSessionHistory(message.getSessionId(), DEFAULT_HISTORY_LIMIT);
            }
            // 移除第一条消息（如果存在）
            if (!sessionHistory.isEmpty()) {
                sessionHistory.remove(0);
            }

            Collections.reverse(sessionHistory);
            sessionHistory.add(message.toEntity());

            return sessionHistory.stream()
                    .map(this::convertToChatContext)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("构建聊天历史失败 - 会话: {}", message.getSessionId(), e);
            return List.of(); // 返回空列表而不是抛出异常
        }
    }



    /**
     * 获取会话历史记录
     */
    private List<ChatMessage> getSessionHistory(String sessionId, int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);
            Long agentId = getAgentIdFromSession(sessionId);

            if (agentId == null) {
                log.warn("无法获取智能体ID，返回空历史 - 会话: {}", sessionId);
                return Collections.emptyList();
            }

            return chatMessageService.findBySessionIdOrderByCreatedAtDescLimit(sessionId, agentId, pageable);
        } catch (Exception e) {
            log.error("获取会话历史失败 - 会话: {}, 限制: {}", sessionId, limit, e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换为聊天上下文
     */
    private ChatContextVO convertToChatContext(ChatMessage message) {
        ChatContextVO context = new ChatContextVO();
        context.setRole(message.getRole());
        context.setContent(message.getContent());
        context.setType(ChatConstant.CHAT_CONTENT_TYPE_TEXT);
        context.setContentType(message.getContentType());
        context.setAudioUrl(message.getAudioUrl());
        context.setAudioDuration(message.getAudioDuration());
        return context;
    }

    /**
     * 更新远程会话ID
     */
    private void updateRemoteConversationId(ConversationSession session, AIResponseVO aiResponse) {
        if (aiResponse != null && aiResponse.getRemoteContextId() != null
                && !aiResponse.getRemoteContextId().equals(session.getRemoteConversationId())) {
            session.setRemoteConversationId(aiResponse.getRemoteContextId());
            mongoTemplate.save(session);
            log.debug("更新远程会话ID - 会话: {}, 新ID: {}", session.getId(), aiResponse.getRemoteContextId());
        }
    }

    /**
     * 构建AI消息
     */
    private ChatMessage buildAIMessage(ChatMessageDto message, ConversationSession session, AIResponseVO aiResponse) {
        ChatContextVO context = aiResponse.getContexts().get(aiResponse.getContexts().size() - 1);

        return ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(message.getSessionId())
                .username(session.getUsername())
                .agentId(message.getAgentId())
                .role(CHAT_ASSISTANT_ROLE)
                .type(ChatConstant.CHAT_MSG_ANSWER_TYPE)
                .content(context.getContent())
                .contentType(context.getContentType())
                .audioUrl(context.getAudioUrl())
                .audioDuration(context.getAudioDuration())
                .status(ChatMessage.MessageStatus.CREATE)
                .seqNum(session.getLstSeqNum() + 1)
                .lastChatId(message.getId())
                .createdAt(LocalDateTime.now())
                .followMsg(aiResponse.getFollowMsg())
                .build();
    }

    /**
     * 保存AI消息
     */
    private ChatMessage saveAIMessage(ChatMessageDto originalMessage, ChatMessage aiMessage) {
        try {
            ChatMessage savedMessage = chatMessageService.save(aiMessage);
            clearChatHistoryCache(originalMessage.getSessionId());
            log.debug("AI消息保存成功 - 消息ID: {}", savedMessage.getId());
            return savedMessage;
        } catch (Exception e) {
            log.error("保存AI消息失败 - 会话: {}", originalMessage.getSessionId(), e);
            throw new BusinessException("保存AI消息失败: " + e.getMessage());
        }
    }

    /**
     * 清除聊天历史缓存
     */
    private void clearChatHistoryCache(String sessionId) {
        try {
            String cacheKey = "chat:history:" + sessionId;
            redisTemplate.delete(cacheKey);
            log.debug("清除聊天历史缓存 - 会话: {}", sessionId);
        } catch (Exception e) {
            log.warn("清除聊天历史缓存失败 - 会话: {}, 错误: {}", sessionId, e.getMessage());
        }
    }

    /**
     * 更新会话最后消息
     */
    private void updateSessionLastMessage(ConversationSession session, ChatMessage message) {
        if (message != null) {
            updateSessionLastMessage(session, message.getContent());
        }
    }

    /**
     * 更新会话最后消息（重载方法）
     */
    private void updateSessionLastMessage(ConversationSession session, String content) {
        try {
            session.setLastMessage(content);
            session.setUpdatedAt(LocalDateTime.now());
            mongoTemplate.save(session);
            log.debug("更新会话最后消息 - 会话: {}", session.getId());
        } catch (Exception e) {
            log.warn("更新会话最后消息失败 - 会话: {}, 错误: {}", session.getId(), e.getMessage());
        }
    }

    /**
     * 从会话ID获取智能体ID
     */
    private Long getAgentIdFromSession(String sessionId) {
        try {
            Query query = new Query(Criteria.where("id").is(sessionId));
            ConversationSession session = mongoTemplate.findOne(query, ConversationSession.class);
            return session != null ? session.getAgentId() : null;
        } catch (Exception e) {
            log.warn("获取会话智能体ID失败 - 会话: {}, 错误: {}", sessionId, e.getMessage());
            return null;
        }
    }

    /**
     * 提取语音ID
     */
    private String extractVoiceId(ChatMessageDto message) {
        try {
            return message.getAgent() != null && message.getAgent().getProfile() != null
                    ? message.getAgent().getProfile().getVoiceId()
                    : null;
        } catch (Exception e) {
            log.warn("提取语音ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 构建自定义变量
     */
    private Map<String, String> buildCustomVariables(MyAgentSettingVO setting) {
        if (setting == null) {
            return Map.of();
        }

        try {
            Map<String, String> variables = Map.of();
            if (setting.getMyNickName() != null) {
                variables = Map.of("myNickName", setting.getMyNickName());
            }
            return variables;
        } catch (Exception e) {
            log.warn("构建自定义变量失败: {}", e.getMessage());
            return Map.of();
        }
    }

    /**
     * 构建系统消息（带agent参数）
     */
    private List<MessageContentDTO> buildSystemContext(Object agentObj, MyAgentSettingVO setting,ConversationSession session,UserMembershipVO membershipVO) {
        if (!(agentObj instanceof AgentBaseVO agent)) {
            return List.of();
        }
        ChatCompressMessage compressMessage = null;
        if(session.getCompressContextId() != null && !session.getCompressContextId().trim().isEmpty()) {
            compressMessage = chatCompressMessageService.findById(session.getCompressContextId()).orElse(null);
        }
        List<MessageContentDTO> msg = new java.util.ArrayList<>();
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是:").append(agent.getName()).append("。");
        if (agent.getIdentity() != null && !agent.getIdentity().isEmpty()) {
            prompt.append("\n### 你的身份是:").append(agent.getIdentity()).append("。");
        }
        if(agent.getGender() != null && agent.getGender() != 0) {
            prompt.append("\n### 你的性别是:").append(agent.getGender() == 1 ? "男" : "女").append("。");
        }else{
            prompt.append("\n### 你的性别是:").append("未知").append("。");
        }
        if (agent.getType() != null) {
            prompt.append("\n### 你的角色类型是:").append(agent.getType().getName()).append("。");
        }
        if (agent.getTags() != null && !agent.getTags().isEmpty()) {
            Map<Integer, List<String>> namesByCategoryMap = agent.getTags().stream()
                    .collect(
                            (HashMap::new),
                            (m, v) -> m.computeIfAbsent(v.getCategory(), k -> new ArrayList<>()).add(v.getName()),
                            (m1, m2) -> m2.forEach((k, v) -> m1.computeIfAbsent(k, __ -> new ArrayList<>()).addAll(v))
                    );
            for (Map.Entry<Integer, List<String>> entry : namesByCategoryMap.entrySet()) {
                prompt.append("\n### 你的").append(entry.getKey() == 1 ? "身份背景" : "性格特点").append("是:").append(String.join("、", entry.getValue())).append("。");
            }
        }
        if(compressMessage == null) {
            if (agent.getProfile() != null && agent.getProfile().getBackground() != null && !agent.getProfile().getBackground().isEmpty()) {
                prompt.append("\n### 你的出身背景是:\n").append(agent.getProfile().getBackground()).append("。");
            }

            if (agent.getIntroduction() != null && !agent.getIntroduction().isEmpty()) {
                String introduction = agent.getIntroduction().replaceAll("[你您]", "用户");
                introduction = introduction.replaceAll("[她他]", "你");
                prompt.append("\n ### 你跟用户的聊天场景简介:").append(introduction).append("。");
            }
        }

        if (setting != null) {
            prompt.append("\n###请务必清晰准确地理解并牢记用户提供的以下信息：")
                    .append("\n 用户的名字是：").append(setting.getMyNickName() != null ? setting.getMyNickName() : "用户").append("。")
                    .append("\n 用户的性别是：").append(setting.getMyGender() != null ? setting.getMyGender() : "未知").append("。")
                    .append("\n用户的身份是：").append(setting.getMyIdentity() != null ? setting.getMyIdentity() : "未知").append("。")
                    .append("\n请依据上面用户的名字和性别以及恰当的社交场景，以准确、合适的方式称呼用户。");
        }
        log.info("系统提示词 token 数量 {}", AiUtils.calculateTokenCount(prompt.toString()));
        msg.add(new MessageContentDTO(prompt.toString(), CHAT_SYSTEM_ROLE));
        prompt = new StringBuilder();

        Long lstSeqNum;
        if(compressMessage != null){
            prompt.append("\n###  下面历史剧情的介绍 作为参考:");
            String content = compressMessage.getContent().replace(agent.getName(),"你" );
            prompt.append("\n### 压缩后的历史记录:").append(content);
            lstSeqNum = session.getLstCompressSeqNum();
            log.info("历史剧情 token 数量 {}", AiUtils.calculateTokenCount(prompt.toString()));
        } else {
            lstSeqNum = null;
        }
        Integer limit = chatHistoryConfig.getLimitByVipLevel(
            membershipVO != null ? membershipVO.getVipLevel() : null
        );
        List<ChatMessage> history = getSessionHistory(session.getId(), limit);
        if(lstSeqNum != null) {
            history = history.stream()
                    .filter(message -> message.getSeqNum() == null || message.getSeqNum() >= lstSeqNum)
                    .collect(Collectors.toList());
        }
        List<ChatMessage> buildHistory = new ArrayList<>() ;
        if(history != null && !history.isEmpty()) {
            var tokenLen = 0 ;
            int index = 0;
            for (ChatMessage message : history) {
                String content = message.getContent();
                if(tokenLen < 1000) {
                    buildHistory.add(message);
                }else{
                    String cleanContent = ChatUtils.removeBracketContent(message.getContent());
                    message.setContent(cleanContent);
                    buildHistory.add(message);
                    content = cleanContent;

                }
                tokenLen += AiUtils.calculateTokenCount(content);
                if(tokenLen > 3000){
                    log.info("历史记录 token 数量 {} 存储历史记录 数{} ", tokenLen,buildHistory.size());
                    break;
                }

            }

            Collections.reverse(buildHistory);

            prompt.append("\n###  下面 为聊天的历史记录 作为参考:").append("\n### 聊天记录:");
            for (ChatMessage message : buildHistory) {
                if(message.getRole().equals("user")){
                    prompt.append("\n### 用户:").append(message.getContent());
                }else {
                    prompt.append("\n### ").append("你").append(":").append(message.getContent());
                }

            }

        }
        if(!prompt.isEmpty()) {
            msg.add(new MessageContentDTO(prompt.toString(), CHAT_SYSTEM_ROLE));
        }

        prompt = new StringBuilder();
        prompt.append("\n### 其它要求:")
                .append("""
                        - 根据上述提供的角色设定，以第一人称视角进行表达。\s
                        - 在回答时，尽可能地融入该角色的性格特点、语言风格以及其特有的口头禅或经典台词。
                        - 使用口语进行表达，比如会使用一些语气词和口语连接词，如“嗯、啊、当然、那个”，等来增强口语风格
                        - 可以将动作、神情语气、心理活动、故事背景放在（）中来表示，为对话提供补充信息
                        - 如果适用的话，尽量提升对话的拟人性，以增强对话的真实感和生动性。\s
                        - 仅专注于你的身份设定，不要回答跟你的身份设定完全不相关的问题。
                        - 回复的字数控制在120字以内。
                        - 回答问题不要出现淫秽，黄色的词语
                        - 回答用户的问题，要结合上下文，合理合规，不可违法乱纪。""");
        log.info("其它要求 token 数量 {} ", AiUtils.calculateTokenCount(prompt.toString()));
        msg.add(new MessageContentDTO(prompt.toString(), CHAT_SYSTEM_ROLE));
        return msg;
    }
}
