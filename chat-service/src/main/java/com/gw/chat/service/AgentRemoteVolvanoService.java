package com.gw.chat.service;


import com.gw.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.chat.dto.MessageContentDTO;
import com.gw.chat.vo.AIResponseVO;
import com.gw.chat.vo.ChatContextVO;
import com.gw.chat.vo.TextToSpeechVO;
import com.volcengine.ark.runtime.model.Usage;

import java.util.List;

public interface AgentRemoteVolvanoService {
    String createConversation(List<MessageContentDTO> systemMessages);

    /**
     * Convert text to speech and return file path and duration
     *
     * @param text    The text content to convert to speech
     * @param voiceId The voice ID to use for the speech
     * @return String containing file path and duration in format "path:duration"
     */
    TextToSpeechVO textConvertSpeech(String text, String voiceId);

    /**
     * 发送聊天消息（带回调）
     *
     * @param conversationId 会话ID
     * @param message 消息列表
     * @param audio 是否需要音频
     * @param voiceId 音色ID
     * @param callback ResourceNotFound错误回调
     * @return AI响应
     * @throws Exception 异常
     */
    AIResponseVO sendChatMessage(String conversationId, List<ChatContextVO> message,
                                 boolean audio, String voiceId, VolcanoChatCallback callback) throws Exception;

    String sendCompressChatMessage(List<ChatContextVO> message) throws Exception;

    /**
     * ResourceNotFound错误回调接口
     */
    interface VolcanoChatCallback {
        void  onChatFinish(Usage usage);

        /**
         * 当遇到ResourceNotFound错误时的回调
         *
         * @param conversationId 会话ID
         * @param systemContext 当前系统上下文
         * @param errorDetails 错误详情
         * @return 创建远程会话的参数
         */
        CreateRemoteSessionParamsDTO onResourceNotFound(String conversationId, SystemContext systemContext, String errorDetails);

        /**
         * 当遇到内容过长错误时的回调
         *
         * @param conversationId 会话ID
         * @param systemContext 当前系统上下文
         * @param errorDetails 错误详情
         * @return 创建远程会话的参数
         */
        CreateRemoteSessionParamsDTO onContentTooLong(String conversationId, SystemContext systemContext, String errorDetails);
    }

    /**
     * 系统上下文信息
     */
    class SystemContext {
        private final String contextCacheId;
        private final List<ChatContextVO> conversationHistory;
        private final long timestamp;


        public SystemContext(String contextCacheId,
                           List<ChatContextVO> conversationHistory) {
            this.contextCacheId = contextCacheId;

            this.conversationHistory = conversationHistory;
            this.timestamp = System.currentTimeMillis();

        }

        public String getContextCacheId() { return contextCacheId; }
        public List<ChatContextVO> getConversationHistory() { return conversationHistory; }
        public long getTimestamp() { return timestamp; }
    }
}
