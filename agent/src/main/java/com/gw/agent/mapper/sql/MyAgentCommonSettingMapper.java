package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.MyAgentCommonSettingEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Optional;

@Mapper
public interface MyAgentCommonSettingMapper extends BaseMapper<MyAgentCommonSettingEntity> {
    @Select("SELECT * FROM t_my_common_agent_setting WHERE username = #{username} Limit 1")
    Optional<MyAgentCommonSettingEntity> findByUsername(@Param("username") String username);

    @Select("SELECT * FROM t_my_common_agent_setting WHERE username = #{username} AND agent_id = #{agentId} LIMIT 1")
    Optional<MyAgentCommonSettingEntity> findByAgentIdAndUsername(@Param("agentId") Long agentId, @Param("username") String username);
}