package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;


@Data
public class MembershipPackageStatusDTO {
    @Schema(description = "套餐ID")
    @Positive(message = "套餐ID必须为正数")
    private Long id;
    @NotNull(message = "状态类型不能为空")
    @Schema(description = "状态: 0-下架, 1-上架")
    private Integer status = 1;
}